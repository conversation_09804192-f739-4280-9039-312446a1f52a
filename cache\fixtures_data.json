[{"league_name": "Europe - UEFA Champions League", "flag_class": "europe", "league_url": "/competitions/international/uefa-champions-league/", "matches": [{"match_id": "5263428", "time": "21:00", "title": "PSG vs Internazionale", "teams": "PSG vs Internazionale", "url": "/match/psg-vs-internazionale/1nou2#5263428", "channels": ["Canal+ France (live stream available)", "M6 (live stream available)", "M6.fr", "Molotov (live stream available)", "Free (live stream available)"]}]}, {"league_name": "Sweden - Allsvenskan", "flag_class": "sweden", "league_url": "/competitions/sweden/allsvenskan/", "matches": [{"match_id": "5210088", "time": "15:00", "title": "Norrköping vs GAIS", "teams": "Norrköping vs GAIS", "url": "/match/gais-vs-norrkoping/2uiw5#5210088", "channels": ["TV4 Play", "Discovery+", "max Extra 2", "VeikkausTV"]}, {"match_id": "5210089", "time": "15:00", "title": "Degerfors vs Öster", "teams": "Degerfors vs Öster", "url": "/match/degerfors-vs-oster/5v64p#5210089", "channels": ["TV4 Play", "Discovery+", "max Extra 3", "VeikkausTV"]}]}, {"league_name": "Sweden - Superettan", "flag_class": "sweden", "league_url": "/competitions/sweden/superettan/", "matches": [{"match_id": "5175501", "time": "13:00", "title": "Västerås SK vs Umeå", "teams": "Västerås SK vs Umeå", "url": "/match/umea-vs-vasteras-sk/1ci9vp#5175501", "channels": ["TV4 Play", "Discovery+", "max Extra 5"]}, {"match_id": "5175502", "time": "15:00", "title": "Sandviken vs Varberg", "teams": "Sandviken vs Varberg", "url": "/match/varberg-vs-sandviken/1f4jgc#5175502", "channels": ["TV4 Play", "Discovery+", "max Extra 6"]}, {"match_id": "5175503", "time": "15:00", "title": "Örgryte vs Trelleborg FF", "teams": "Örgryte vs Trelleborg FF", "url": "/match/trelleborg-ff-vs-orgryte/2kozs#5175503", "channels": ["TV4 Play", "Discovery+", "max Extra 7"]}]}, {"league_name": "Turkey - Super Lig", "flag_class": "turkey", "league_url": "/competitions/turkey/super-lig/", "matches": [{"match_id": "4981354", "time": "15:00", "title": "Fenerbahçe vs Konyaspor", "teams": "Fenerbahçe vs Konyaspor", "url": "/match/fenerbahce-vs-konyaspor/3l1pj#4981354", "channels": ["beIN Sports MAX 4 (live stream available)", "beIN SPORTS CONNECT (live stream available)", "Free (live stream available)", "myCANAL (live stream available)", "Sport TV1"]}, {"match_id": "4981356", "time": "18:00", "title": "Adana Demirspor vs Gaziantep FK", "teams": "Adana Demirspor vs Gaziantep FK", "url": "/match/gaziantep-fk-vs-adana-demirspor/ewqqq#4981356", "channels": ["Digiturk Play", "beIN Sports 2 Turkey", "beIN CONNECT Turkey", "TOD"]}, {"match_id": "4981359", "time": "18:00", "title": "Alanyaspor vs Sivasspor", "teams": "Alanyaspor vs Sivasspor", "url": "/match/sivasspor-vs-alanyaspor/82p6q#4981359", "channels": ["Digiturk Play", "beIN Sports 1 Turkey", "beIN CONNECT Turkey", "TOD"]}]}, {"league_name": "USA/Canada - Major League Soccer", "flag_class": "us-ca", "league_url": "/competitions/united-states/major-league-soccer/", "matches": [{"match_id": "5138111", "time": "22:30", "title": "Nashville SC vs New York City", "teams": "Nashville SC vs New York City", "url": "/match/new-york-city-vs-nashville-sc/4e5uoz#5138111", "channels": ["MLS Season Pass on Apple TV (live stream available)", "EA Sports FC"]}]}]