import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'package:cat_tv/models/fixture.dart';
import 'package:puppeteer/puppeteer.dart'; // Import Puppeteer
import 'package:cat_tv/utils/ad_blocker.dart';

class FixturesScraper {
  static const String _baseUrl = 'https://www.livesoccertv.com';
  static const String _schedulesUrl = '$_baseUrl/schedules/';
  static const String _cacheFilePath = 'cache/fixtures_data.json';
  static const String _lastUpdateFilePath = 'cache/last_fixtures_update.txt';

  static Future<bool> shouldUpdateFixtures() async {
    try {
      final lastUpdateFile = File(_lastUpdateFilePath);
      if (!await lastUpdateFile.exists()) {
        return true; // No previous update, should update
      }

      final lastUpdateStr = await lastUpdateFile.readAsString();
      final lastUpdate = DateTime.tryParse(lastUpdateStr);
      if (lastUpdate == null) {
        return true; // Invalid date, should update
      }

      final now = DateTime.now();
      final difference = now.difference(lastUpdate);

      // Update if more than 24 hours have passed
      return difference.inHours >= 24;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking last update: $e');
      }
      return true; // Error occurred, should update
    }
  }

  static Future<List<FixtureLeague>> scrapeFixtures() async {
    Browser? browser;
    try {
      if (kDebugMode) {
        print('Launching browser for scraping...');
      }
      browser = await puppeteer.launch(
        headless: !kDebugMode, // Headless in production, visible in debug mode
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      );
      final page = await browser.newPage();
      await page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      );

      await page.setRequestInterception(true);
      page.onRequest.listen((request) {
        if (AdBlocker.isBlocked(request.url)) {
          request.abort(); // Block the request
        } else {
          request.continueRequest();
        }
      });

      if (kDebugMode) {
        print('Navigating to: $_schedulesUrl');
      }
      await AdBlocker.loadEasylist();

      // Navigate to the page first
      await page.goto(
        _schedulesUrl,
        wait: Until.domContentLoaded,
        timeout: const Duration(seconds: 30),
      );

      // Handle privacy notice immediately after page load
      await _handlePrivacyNotice(page);

      // Alternative approach using JavaScript execution
      await _handlePrivacyNoticeWithJS(page);

      // Wait for the page to fully load after handling privacy notice
      await page.waitForSelector('main', timeout: Duration(seconds: 15));

      // Get the final content
      final content = await page.content ?? '';
      if (content.isEmpty) {
        throw Exception('Page content is empty after privacy notice handling');
      }

      if (kDebugMode) {
        print('Started scraping with content length: ${content.length}');
      }

      final document = html_parser.parse(content);
      final leagues = <FixtureLeague>[];

      // Parse schedules table
      final parsedData = _parseSchedulesTable(document);
      leagues.addAll(parsedData);

      if (kDebugMode) {
        print('Scraped ${leagues.length} leagues with matches');
      }
      return leagues;
    } catch (e) {
      if (kDebugMode) {
        print('Error during Puppeteer scraping: $e');
      }
      rethrow;
    } finally {
      if (browser != null) {
        if (kDebugMode) {
          print('Closing browser.');
        }
        await browser.close();
      }
    }
  }

  // Helper method to handle privacy notice/cookie consent
  static Future<void> _handlePrivacyNotice(Page page) async {
    try {
      if (kDebugMode) {
        print('Checking for privacy notice...');
      }

      // Wait a bit for the privacy notice to appear
      await Future.delayed(const Duration(seconds: 2));

      // Try multiple selectors for the privacy notice
      final privacySelectors = [
        '#snigel-cmp-framework',
        '.snigel-cmp-framework',
        '[id*="cmp"]',
        '[class*="cmp"]',
        '[class*="consent"]',
        '[class*="privacy"]',
      ];

      bool privacyNoticeFound = false;

      for (final selector in privacySelectors) {
        try {
          final isVisible = await page.evaluate(
            '''
            (selector) => {
              const el = document.querySelector(selector);
              if (!el) return false;
              const style = window.getComputedStyle(el);
              return style.display !== 'none' &&
                     style.visibility !== 'hidden' &&
                     style.opacity !== '0' &&
                     el.offsetHeight > 0 &&
                     el.offsetWidth > 0;
            }
          ''',
            args: [selector],
          );

          if (isVisible == true) {
            privacyNoticeFound = true;
            if (kDebugMode) {
              print('Privacy notice found with selector: $selector');
            }
            break;
          }
        } catch (e) {
          // Continue to next selector
          continue;
        }
      }

      if (privacyNoticeFound) {
        // Try to click accept button with multiple possible selectors
        final acceptSelectors = [
          '#accept-choices',
          '[id*="accept"]',
          '[class*="accept"]',
          'button:contains("Accept")',
          'button:contains("accept")',
          'button:contains("ACCEPT")',
          '.sn-b-def.sn-blue',
          'button[class*="blue"]',
        ];

        bool clicked = false;
        for (final selector in acceptSelectors) {
          try {
            await page.click(selector);
            clicked = true;
            if (kDebugMode) {
              print(
                'Successfully clicked accept button with selector: $selector',
              );
            }
            break;
          } catch (e) {
            // Continue to next selector
            continue;
          }
        }

        if (clicked) {
          // Wait for privacy notice to disappear
          await Future.delayed(const Duration(seconds: 3));

          // Verify privacy notice is gone
          try {
            await page.waitForFunction('''
              () => {
                const cmpFramework = document.querySelector('#snigel-cmp-framework');
                return !cmpFramework ||
                       cmpFramework.style.display === 'none' ||
                       cmpFramework.classList.contains('hidden');
              }
            ''', timeout: Duration(seconds: 10));

            if (kDebugMode) {
              print('Privacy notice successfully dismissed');
            }
          } catch (e) {
            if (kDebugMode) {
              print('Privacy notice might still be visible: $e');
            }
          }
        } else {
          if (kDebugMode) {
            print('Could not find accept button to click');
          }
        }
      } else {
        if (kDebugMode) {
          print('No privacy notice detected');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling privacy notice: $e');
      }
    }
  }

  // Alternative JavaScript-based approach to handle privacy notice
  static Future<void> _handlePrivacyNoticeWithJS(Page page) async {
    try {
      if (kDebugMode) {
        print('Attempting JavaScript-based privacy notice handling...');
      }

      final result = await page.evaluate('''
        () => {
          // Function to check if element is visible
          function isVisible(element) {
            if (!element) return false;
            const style = window.getComputedStyle(element);
            return style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0' &&
                   element.offsetHeight > 0 &&
                   element.offsetWidth > 0;
          }

          // Look for privacy notice
          const privacySelectors = [
            '#snigel-cmp-framework',
            '.snigel-cmp-framework',
            '[id*="cmp"]',
            '[class*="cmp"]',
            '[class*="consent"]'
          ];

          let privacyElement = null;
          for (const selector of privacySelectors) {
            const el = document.querySelector(selector);
            if (el && isVisible(el)) {
              privacyElement = el;
              break;
            }
          }

          if (!privacyElement) {
            return { success: false, message: 'No privacy notice found' };
          }

          // Look for accept button
          const acceptSelectors = [
            '#accept-choices',
            '[id*="accept"]',
            'button:contains("Accept")',
            '.sn-b-def.sn-blue',
            'button[class*="blue"]'
          ];

          for (const selector of acceptSelectors) {
            try {
              const button = document.querySelector(selector);
              if (button && isVisible(button)) {
                button.click();
                return { success: true, message: 'Clicked accept button: ' + selector };
              }
            } catch (e) {
              continue;
            }
          }

          // If no button found, try to remove the privacy notice directly
          try {
            privacyElement.style.display = 'none';
            privacyElement.remove();
            return { success: true, message: 'Removed privacy notice directly' };
          } catch (e) {
            return { success: false, message: 'Could not handle privacy notice' };
          }
        }
      ''');

      if (kDebugMode) {
        print('JavaScript privacy notice result: $result');
      }

      // Wait a bit after handling
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      if (kDebugMode) {
        print('Error in JavaScript privacy notice handling: $e');
      }
    }
  }

  static List<FixtureLeague> _parseSchedulesTable(Document document) {
    final parsedData = <FixtureLeague>[];
    if (kDebugMode) {
      print('\n--- Starting schedules table parsing ---');
    }

    // Find the main element
    final mainElement = document.querySelector('main');
    if (mainElement == null) {
      if (kDebugMode) {
        print('Could not find the <main> element.');
      }
      return parsedData;
    }

    // Find the schedules table
    final schedulesTable = mainElement.querySelector('table.schedules');
    if (schedulesTable == null) {
      if (kDebugMode) {
        print('Could not find the schedules table with class "schedules".');
      }
      return parsedData;
    }

    if (kDebugMode) {
      print('--- Found schedules table. ---');
    }
    final tbody = schedulesTable.querySelector('tbody');
    if (tbody == null) {
      if (kDebugMode) {
        print('Could not find the <tbody> element within the schedules table.');
      }
      return parsedData;
    }

    if (kDebugMode) {
      print('--- Found tbody. Parsing rows... ---');
    }
    FixtureLeague? currentLeague;

    // Get all direct tr children
    final rows = tbody.querySelectorAll('tr');

    for (final row in rows) {
      final classes = row.classes;

      if (classes.contains('sortable_comp')) {
        // This is a league header row
        final league = _parseLeagueRow(row);
        if (league != null) {
          currentLeague = league;
          parsedData.add(currentLeague);
        }
      } else if (classes.contains('matchrow')) {
        // This is a match row
        final matchId = row.attributes['id'];
        if (kDebugMode) {
          print('  Processing Match Row (ID: $matchId)');
        }

        if (currentLeague != null) {
          final match = _parseMatchRow(row);
          if (match != null) {
            currentLeague.matches.add(match);
            if (kDebugMode) {
              print('    Added match to current league: ${match.teams}');
            }
          }
        } else {
          if (kDebugMode) {
            print(
              '    Warning: Found match row without a preceding league header. Skipping.',
            );
          }
        }
      }
    }

    if (kDebugMode) {
      print('\n--- Finished schedules table parsing ---');
    }
    return parsedData;
  }

  static FixtureLeague? _parseLeagueRow(Element row) {
    try {
      // Look for league header row with class 'sortable_comp'
      if (!row.classes.contains('sortable_comp')) {
        return null;
      }

      final compRowTd = row.querySelector('td.r_comprow');
      if (compRowTd == null) return null;

      // Get league name from span with class 'flag'
      final leagueNameSpan = compRowTd.querySelector('span.flag');
      if (leagueNameSpan == null) return null;

      final leagueName = leagueNameSpan.text.trim();
      if (leagueName.isEmpty) return null;

      // Get flag class (all classes except 'flag')
      final flagClasses =
          leagueNameSpan.classes.where((c) => c != 'flag').toList();
      final flagClass = flagClasses.isNotEmpty ? flagClasses.first : 'world';

      // Get league URL from a tag with class 'flag eurl'
      final leagueUrlA = compRowTd.querySelector('a.flag.eurl');
      final leagueUrl = leagueUrlA?.attributes['href'] ?? '';

      if (kDebugMode) {
        print('  Parsed League: $leagueName');
      }
      if (kDebugMode) {
        print('  League URL: $leagueUrl');
      }

      return FixtureLeague(
        leagueName: leagueName,
        flagClass: flagClass,
        leagueUrl: leagueUrl,
        matches: [], // Matches will be added separately
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing league row: $e');
      }
      return null;
    }
  }

  static FixtureMatch? _parseMatchRow(Element row) {
    try {
      final matchId = row.attributes['id'] ?? '';

      // Get match time from timecol
      final timeCol = row.querySelector('td.timecol');
      final timeSpan = timeCol?.querySelector('span.ts');
      final matchTime = timeSpan?.text.trim() ?? 'N/A';
      if (kDebugMode) {
        print('    Match Time: $matchTime');
      }

      // Get match details from match td
      final matchTd = row.querySelector('td#match');
      final matchLink = matchTd?.querySelector('a');

      String matchTitle = 'N/A';
      String matchTeams = 'N/A';
      String matchUrl = 'N/A';

      if (matchLink != null) {
        if (kDebugMode) {
          print('    Match Link found: ${matchLink.outerHtml}');
        }
        matchTitle = matchLink.attributes['title']?.trim() ?? 'N/A';
        matchTeams = matchLink.text.trim();
        matchUrl = matchLink.attributes['href'] ?? 'N/A';
      } else {
        if (kDebugMode) {
          print('    Match Link not found.');
        }
      }

      if (kDebugMode) {
        print('    Match Title: $matchTitle');
        print('    Match Teams: $matchTeams');
        print('    Match URL: $matchUrl');
      }

      // Get channels from channels td
      final channelsTd = row.querySelector('td#channels');
      final channelsDiv = channelsTd?.querySelector('div.mchannels');
      final channels = <String>[];

      if (channelsDiv != null) {
        final channelLinks = channelsDiv.querySelectorAll('a');
        for (final channelLink in channelLinks) {
          final channelName = channelLink.attributes['title'];
          if (channelName != null && channelName.trim().isNotEmpty) {
            channels.add(channelName.trim());
          }
        }
      }
      if (kDebugMode) {
        print('    Channels: $channels');
      }

      return FixtureMatch(
        matchId: matchId,
        time: matchTime,
        title: matchTitle,
        teams: matchTeams,
        url: matchUrl,
        channels: channels,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing match row: $e');
      }
      return null;
    }
  }

  static Future<void> saveFixturesToCache(List<FixtureLeague> leagues) async {
    try {
      // Ensure cache directory exists
      final cacheDir = Directory('cache');
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Convert to JSON
      final jsonData =
          leagues
              .map(
                (league) => {
                  'league_name': league.leagueName,
                  'flag_class': league.flagClass,
                  'league_url': league.leagueUrl,
                  'matches':
                      league.matches
                          .map(
                            (match) => {
                              'match_id': match.matchId,
                              'time': match.time,
                              'title': match.title,
                              'teams': match.teams,
                              'url': match.url,
                              'channels': match.channels,
                            },
                          )
                          .toList(),
                },
              )
              .toList();

      // Save to file
      final file = File(_cacheFilePath);
      await file.writeAsString(json.encode(jsonData));

      // Update last update timestamp
      final lastUpdateFile = File(_lastUpdateFilePath);
      await lastUpdateFile.writeAsString(DateTime.now().toIso8601String());

      if (kDebugMode) {
        print('Fixtures saved to cache: ${leagues.length} leagues');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving fixtures to cache: $e');
      }
      rethrow;
    }
  }

  static Future<List<FixtureLeague>> updateFixturesIfNeeded() async {
    try {
      if (await shouldUpdateFixtures()) {
        if (kDebugMode) {
          print('Updating fixtures...');
        }
        try {
          final leagues = await scrapeFixtures();
          await saveFixturesToCache(leagues);
          return leagues;
        } catch (e) {
          if (kDebugMode) {
            print('Scraping failed, creating sample data: $e');
          }
          // If scraping fails, create some sample data for testing
          final sampleLeagues = _createSampleFixtures();
          await saveFixturesToCache(sampleLeagues);
          return sampleLeagues;
        }
      } else {
        if (kDebugMode) {
          print('Fixtures are up to date, loading from cache...');
        }
        return await loadFixturesFromCache();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating fixtures, trying to load from cache: $e');
      }
      final cachedLeagues = await loadFixturesFromCache();
      if (cachedLeagues.isEmpty) {
        // If cache is also empty, return sample data
        return _createSampleFixtures();
      }
      return cachedLeagues;
    }
  }

  static List<FixtureLeague> _createSampleFixtures() {
    return [
      FixtureLeague(
        leagueName: 'Premier League',
        flagClass: 'england',
        leagueUrl: '/competitions/england/premier-league/',
        matches: [
          FixtureMatch(
            matchId: '1001',
            time: '15:00',
            title: 'Manchester United vs Liverpool',
            teams: 'Manchester United vs Liverpool',
            url: '/match/manchester-united-vs-liverpool/test1',
            channels: ['Sky Sports', 'NBC Sports', 'DAZN'],
          ),
          FixtureMatch(
            matchId: '1002',
            time: '17:30',
            title: 'Arsenal vs Chelsea',
            teams: 'Arsenal2 - 1Chelsea',
            url: '/match/arsenal-vs-chelsea/test2',
            channels: ['BT Sport', 'ESPN', 'beIN Sports'],
          ),
        ],
      ),
      FixtureLeague(
        leagueName: 'La Liga',
        flagClass: 'spain',
        leagueUrl: '/competitions/spain/la-liga/',
        matches: [
          FixtureMatch(
            matchId: '2001',
            time: '20:00',
            title: 'Real Madrid vs Barcelona',
            teams: 'Real Madrid vs Barcelona',
            url: '/match/real-madrid-vs-barcelona/test3',
            channels: ['ESPN+', 'Movistar+', 'beIN Sports'],
          ),
        ],
      ),
    ];
  }

  static Future<List<FixtureLeague>> loadFixturesFromCache() async {
    try {
      final file = File(_cacheFilePath);
      if (!await file.exists()) {
        return [];
      }

      final jsonString = await file.readAsString();
      final List<dynamic> jsonData = json.decode(jsonString);

      return jsonData.map((league) => FixtureLeague.fromJson(league)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading fixtures from cache: $e');
      }
      return [];
    }
  }
}
