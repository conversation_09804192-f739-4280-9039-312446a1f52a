import 'package:flutter/material.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/pages/fixtures_page.dart';
import 'package:cat_tv/pages/webview_page.dart';
import 'package:cat_tv/repositories/channel_repository.dart'; // Needed for isChannelSourceExternal

class SourceSelectionPage extends StatefulWidget {
  final Channel channel;
  final List<String> sources;
  final ChannelRepository channelRepository; // Add this

  const SourceSelectionPage({
    super.key,
    required this.channel,
    required this.sources,
    required this.channelRepository, // Add this
  });

  @override
  State<SourceSelectionPage> createState() => _SourceSelectionPageState();
}

class _SourceSelectionPageState extends State<SourceSelectionPage> {
  late String _currentSourceUrl;

  @override
  void initState() {
    super.initState();
    _currentSourceUrl = widget.sources.first;
    _loadSource(
      _currentSourceUrl,
      initialLoad: true,
    ); // Load the first source automatically
  }

  void _loadSource(String sourceUrl, {bool initialLoad = false}) async {
    setState(() {
      _currentSourceUrl = sourceUrl;
    });

    final isExternal = await widget.channelRepository.isChannelSourceExternal(
      widget.channel.channelId,
    );

    if (!mounted) return;

    if (isExternal) {
      if (initialLoad) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (_) =>
                    WebViewPage(channel: widget.channel, channelUrl: sourceUrl),
          ),
        );
      } else {
        // If not initial load, just update the player within this page
        // For now, we'll navigate, but ideally, the player would be embedded here.
        // Since the player is on FixturesPage/WebViewPage, we'll navigate.
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (_) =>
                    WebViewPage(channel: widget.channel, channelUrl: sourceUrl),
          ),
        );
      }
    } else {
      if (initialLoad) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (_) => FixturesPage(
                  channel: widget.channel,
                  channelUrl: sourceUrl,
                ),
          ),
        );
      } else {
        // If not initial load, just update the player within this page
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder:
                (_) => FixturesPage(
                  channel: widget.channel,
                  channelUrl: sourceUrl,
                ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.channel.name)),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              spacing: 8.0,
              children: List.generate(widget.sources.length, (index) {
                final source = widget.sources[index];
                return ChoiceChip(
                  label: Text('Source ${index + 1}'),
                  selected: _currentSourceUrl == source,
                  onSelected: (selected) {
                    if (selected) {
                      _loadSource(source);
                    }
                  },
                );
              }),
            ),
          ),
          // Placeholder for the player or content area
          Expanded(
            child: Center(child: Text('Loading source: $_currentSourceUrl')),
          ),
        ],
      ),
    );
  }
}
